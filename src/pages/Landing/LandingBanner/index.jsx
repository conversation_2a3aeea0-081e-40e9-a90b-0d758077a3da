import React, { useState, useCallback, useMemo, lazy, Suspense } from 'react'
import GangImg from '../../../components/ui-kit/icons/webp/Gang.webp'
import shieldWhite from '../../../components/ui-kit/icons/webp/shield-white.webp'
import { Swiper, SwiperSlide } from 'swiper/react'
import 'swiper/css'
import 'swiper/css/navigation'
import { Navigation, Keyboard } from 'swiper/modules'
import { Box, Button, Grid, InputAdornment, TextField, Typography } from '@mui/material'
import MailOutlineIcon from '@mui/icons-material/MailOutline'
import ArrowCircleRightOutlinedIcon from '@mui/icons-material/ArrowCircleRightOutlined'
import { usePortalStore } from '../../../store/store'
import LazyImage from '../../../utils/lazyImage'
import OptimizedBannerBackground from './OptimizedBannerBackground'
import CriticalResourcePreloader from './CriticalResourcePreloader'

// Lazy load Signup modal
const Signup = lazy(() => import('../../../components/Modal/Signup'))

// Import provider images but load them lazily in the slider
import Provider1 from '../../../components/ui-kit/icons/webp/provider-1.webp'
import Provider2 from '../../../components/ui-kit/icons/webp/provider-2.webp'
import Provider3 from '../../../components/ui-kit/icons/webp/provider-3.webp'
import Provider4 from '../../../components/ui-kit/icons/webp/provider-4.webp'
import Provider5 from '../../../components/ui-kit/icons/webp/provider-5.webp'
import Provider6 from '../../../components/ui-kit/icons/webp/provider-6.webp'
import Provider7 from '../../../components/ui-kit/icons/webp/provider-7.webp'

const providerImages = [Provider1, Provider2, Provider3, Provider4, Provider5, Provider6, Provider7]

const LandingBanner = () => {
  const portalStore = usePortalStore()
  const [email, setEmail] = useState('')
  const [errorMessage, setErrorMessage] = useState('')

  const handleClaimWelcomeOffer = useCallback(() => {
    const trimmedEmail = email.trim()
    if (!trimmedEmail) {
      setErrorMessage('Email is required.')
      return
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(trimmedEmail)) {
      setErrorMessage('Please enter a valid email address.')
      return
    }

    setErrorMessage('')
    portalStore.openPortal(
      () => (
        <Suspense fallback={null}>
          <Signup email={trimmedEmail} />
        </Suspense>
      ),
      'loginModal'
    )
  }, [email, portalStore])

  const handleEmailChange = useCallback(
    (event) => {
      const value = event.target.value
      setEmail(value)
      if (errorMessage) setErrorMessage('')
    },
    [errorMessage]
  )

  const renderProviderSlides = useMemo(
    () =>
      providerImages.map((imgSrc, index) => (
        <SwiperSlide key={index}>
          <figure style={{ margin: 0, textAlign: 'center' }}>
            <img
              src={imgSrc}
              alt={`Game Provider ${index + 1}`}
              width={100}
              height={60}
              style={{
                maxWidth: '100%',
                height: 'auto',
                aspectRatio: '5/3', // Provider logo aspect ratio for layout stability
                objectFit: 'contain',
                display: 'block'
              }}
              loading={index < 3 ? 'eager' : 'lazy'} // Load first 3 eagerly, rest lazily
              decoding='async'
            />
          </figure>
        </SwiperSlide>
      )),
    []
  )

  return (
    <>
      <CriticalResourcePreloader />
      <OptimizedBannerBackground className='landing-banner-section jackpot-enabled'>
        <Box className='landing-banner-content' name='Discover_1'>
        <Typography variant='h1'>
          Discover <span>America's #1 Social Casino</span> Platform
        </Typography>
        <Typography>Join countless players! Get your 425% extra NOW!</Typography>
        <form className='landiang-email-input' data-tracking='Home.Discover.EnterEmail.Fld'>
          <TextField
            placeholder='Enter your email now'
            variant='outlined'
            id='email'
            name='email'
            value={email}
            onChange={handleEmailChange}
            error={Boolean(errorMessage)}
            helperText={errorMessage}
            InputProps={{
              startAdornment: (
                <InputAdornment position='start'>
                  <MailOutlineIcon />
                </InputAdornment>
              )
            }}
          />
        </form>
        <Grid className='btn-wrap'>
          <Button
            className='btn btn-primary'
            onClick={handleClaimWelcomeOffer}
            data-tracking='Home.Discover.ClaimWelcomeOffer.Btn'
          >
            <ArrowCircleRightOutlinedIcon />
            Claim Welcome Offer
          </Button>
        </Grid>
        <Grid className='banner-disclaimer'>
          <LazyImage src={shieldWhite} alt='Secure' lazy={false} />
          <Typography>Trusted by countless players across the US</Typography>
        </Grid>
      </Box>

      <section className='provider-section'>
        <Grid className='group-graphic'>
          <LazyImage
            src={GangImg}
            alt='Casino-Gangue'
            lazy={false}
            style={{
              width: '100%',
              height: 'auto',
              aspectRatio: '1239/556', // Actual intrinsic aspect ratio
              display: 'block'
            }}
          />
          <Grid className='provider-content-wrap'>
            <Typography variant='h3'>Hottest Games Provided By:</Typography>
            <Grid className='provider-slider-content'>
              <Swiper
                slidesPerView={6}
                spaceBetween={30}
                keyboard
                navigation
                loop
                modules={[Navigation, Keyboard]}
                className='mySwiper'
                breakpoints={{
                  0: {
                    slidesPerView: 3,
                    spaceBetween: 10
                  },
                  768: {
                    slidesPerView: 4,
                    spaceBetween: 20
                  },
                  1024: {
                    slidesPerView: 6,
                    spaceBetween: 30
                  }
                }}
              >
                {renderProviderSlides}
              </Swiper>
            </Grid>
          </Grid>
        </Grid>
      </section>
    </OptimizedBannerBackground>
    </>
  )
}

export default LandingBanner
