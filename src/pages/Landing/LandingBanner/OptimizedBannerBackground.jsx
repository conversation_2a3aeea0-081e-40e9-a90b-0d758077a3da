import React, { useState, useEffect } from 'react'
import { useTheme, useMediaQuery } from '@mui/material'

const OptimizedBannerBackground = ({ children, className }) => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const [backgroundImage, setBackgroundImage] = useState(null)

  useEffect(() => {
    // Dynamically import the background image for better performance
    import('../../../components/ui-kit/icons/webp/landing-banner.webp')
      .then(module => {
        setBackgroundImage(module.default)
      })
      .catch(() => {
        // Handle error silently, will fallback to CSS styles
      })
  }, [])

  // Apply background image via inline styles for better control
  const backgroundStyle = backgroundImage ? {
    backgroundImage: `url(${backgroundImage})`,
    backgroundSize: isMobile ? 'cover' : '100%',
    backgroundRepeat: 'no-repeat',
    backgroundPosition: isMobile ? 'center' : '100% -10%'
  } : {}

  return (
    <section
      className={className}
      data-tracking='Home.Discover.Section'
      style={backgroundStyle}
    >
      {children}
    </section>
  )
}

export default OptimizedBannerBackground
