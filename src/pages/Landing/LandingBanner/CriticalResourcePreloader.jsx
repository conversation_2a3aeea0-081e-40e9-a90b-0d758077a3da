import React, { useEffect } from 'react'

/**
 * Component to preload critical resources for better LCP performance
 * This should be rendered early in the component tree
 */
const CriticalResourcePreloader = () => {
  useEffect(() => {
    // Preload critical images that affect LCP
    const criticalImages = [
      () => import('../../../components/ui-kit/icons/webp/landing-banner.webp'),
      () => import('../../../components/ui-kit/icons/webp/Gang.webp'),
      () => import('../../../components/ui-kit/icons/webp/shield-white.webp')
    ]

    // Preload images with high priority
    criticalImages.forEach((importFn, index) => {
      importFn().then(module => {
        const link = document.createElement('link')
        link.rel = 'preload'
        link.as = 'image'
        link.href = module.default
        link.fetchPriority = index === 0 ? 'high' : 'low' // First image (banner) gets high priority
        document.head.appendChild(link)
      }).catch(error => {
        console.warn('Failed to preload critical image:', error)
      })
    })

    // Preload critical fonts if any
    const criticalFonts = [
      // Add any critical font URLs here if needed
    ]

    criticalFonts.forEach(fontUrl => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'font'
      link.type = 'font/woff2'
      link.href = fontUrl
      link.crossOrigin = 'anonymous'
      document.head.appendChild(link)
    })

    // Cleanup function to remove preload links when component unmounts
    return () => {
      const preloadLinks = document.querySelectorAll('link[rel="preload"]')
      preloadLinks.forEach(link => {
        if (link.href.includes('landing-banner') || 
            link.href.includes('Gang.webp') || 
            link.href.includes('shield-white')) {
          document.head.removeChild(link)
        }
      })
    }
  }, [])

  // This component doesn't render anything visible
  return null
}

export default CriticalResourcePreloader
