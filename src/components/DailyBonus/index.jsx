import React, { Fragment, useEffect, useState } from 'react'
import { Button, Grid, IconButton, Typography, DialogContent, Box, Stack, LinearProgress, CircularProgress } from '@mui/material'
import moment from 'moment'
import CloseIcon from '@mui/icons-material/Close'
import { useClaimDailyBonusMutation, useGetDailyBonusMutation } from '../../reactQuery/bonusQuery'
import { toast } from 'react-hot-toast'
import { useGetProfileMutation } from '../../reactQuery'
import { useUserStore } from '../../store/useUserSlice'
import { usePortalStore } from '../../store/userPortalSlice'
import SpecialPurchaseModal from '../SpecialPurchaseModal'
import bonusRibbon from '../../components/ui-kit/icons/png/bonus-ribbon.png'
import Day1 from '../../components/ui-kit/icons/png/streak-day-1.png'
import Day3 from '../../components/ui-kit/icons/png/streak-day-3.png'
import Day5 from '../../components/ui-kit/icons/png/streak-day-5.png'
import Day7 from '../../components/ui-kit/icons/png/streak-day-7.png'
import StreakTimer from '../CountDownTimer/streakTimer'
import useSeon from '../../utils/useSeon'
import TagManager from 'react-gtm-module'
import DayBox from './DayBox'
import ScratchCardComponent from '../../components/ScratchCard/ScratchCardComponent'
/* eslint-disable multiline-ternary */

const DailyBonus = ({ dailyBonus, resetData }) => {
  const setUserDetails = useUserStore((state) => state.setUserDetails)
  const portalStore = usePortalStore((state) => state)
  const [isLoading, setIsLoading] = useState(false)
  const [enableAnimation, setEnableAnimation] = useState(false)
  const [progressBar, setProgressBar] = useState(0)
  const [remainingTime, setRemainingTime] = useState(dailyBonus?.remainingTime)
  const [claimedDays, setClaimedDays] = useState(dailyBonus?.claimedDays || [])
  const [data, setDailyBonus] = useState(dailyBonus?.streakDailyBonusDetails || [])
  const sessionId = useSeon()

  const mutationGetDailyBonus = useGetDailyBonusMutation({
    onSuccess: (res) => {
      setRemainingTime('00:00:00')
      setClaimedDays(res?.data?.data?.claimedDays)
      setDailyBonus(res?.data?.data?.streakDailyBonusDetails)
    }
  })

  const mutationClaimDailyBonus = useClaimDailyBonusMutation({
    onSuccess: (res) => {
      TagManager.dataLayer({
        dataLayer: {
          event: 'bonus',
          bonus_type: 'daily_bonus',
          user_id: res?.data?.userWallet?.ownerId,
          gcCoin: res?.data?.userWallet?.gcCoin,
          scCoin: res?.data?.userWallet?.totalScCoin
        }
      })
      setRemainingTime('23:59:59')
      setEnableAnimation(true)
      setProgressBar((claimedDays.length / 7) * 100)
      setIsLoading(false)
      toast.success(res?.data?.message)
      localStorage.setItem('allowedUserAccess', true)
     
      if(res?.data?.scratchCardBonus){
        portalStore.openPortal(() => <ScratchCardComponent scratchCardBonus={res?.data?.scratchCardBonus} 
         userBonusId={res?.data?.userBonusId} 
         rewardType={res?.data?.rewardType} 
         parentMessage={res?.data?.parentMessage}
         childMessage={res?.data?.childMessage}
         />, 'bonusStreak')
      }else {
         setTimeout(() => portalStore.closePortal(), 1000)
      }
      getProfileMutation.mutate()
    },
    onError: (error) =>{
      portalStore.closePortal();
    }
  })

  const getProfileMutation = useGetProfileMutation({
    onSuccess: (res) => {
      setUserDetails(res?.data?.data)
      if (res?.data?.data?.welcomePurchaseBonusApplicable) {
        portalStore.openPortal(() => <SpecialPurchaseModal />, 'termsNConditionModal')
      }
    }
  })

  const handleClose = () => {
    portalStore.closePortal()
    getProfileMutation.mutate()
  }

  // **Effect for handling timer and API call**
  useEffect(() => {
    if (resetData !== '00:00:00') {
      if (remainingTime === '00:00:00') {
        mutationGetDailyBonus.mutate()
        return
      }

      let totalSeconds = moment.duration(dailyBonus?.resetTime).asSeconds()
      if (totalSeconds <= 0) return

      const timer = setInterval(() => {
        totalSeconds -= 1
        setRemainingTime(moment.utc(totalSeconds * 1000).format('HH:mm:ss'))
        if (totalSeconds <= 0) {
          clearInterval(timer)
          mutationGetDailyBonus.mutate()
        }
      }, 1000)

      return () => clearInterval(timer)
    }
  }, [remainingTime, resetData])

  // **Effect for setting progress bar**
  useEffect(() => {
    setProgressBar((claimedDays.length / 7) * 100)
  }, [claimedDays])

  const claimDailyBonus = (id) => {
    setIsLoading(true)
    const bonusId = id || data.find((x) => x.isClaimableToday)?.bonusId
    mutationClaimDailyBonus.mutate({ bonusId, cancel: false, sessionKey: sessionId, rtyuioo: sessionId === ' ' })
  }

  return (
    <Grid className='bonus-streak-modal'>
      <Grid className='modal-close'>
        <IconButton edge='start' color='inherit' onClick={handleClose} aria-label='close'>
          <CloseIcon />
        </IconButton>
      </Grid>
      <Box className='bonus-ribbon '>
        <img src={bonusRibbon} loading='eager' />
        <Typography variant='h3'>DAILY LOGIN BONUS</Typography>
      </Box>
      <Box className='next-timer'>
        <button>
          <Typography variant='body1'>Next In:</Typography>
          <Typography variant='h5'>
            {' '}
            <StreakTimer
              initialTime={remainingTime}
              onTimerEnd={() => {
                if (resetData !== '00:00:00') {
                  mutationGetDailyBonus.mutate()
                }
              }}
            />
          </Typography>
        </button>
      </Box>
      <DialogContent>
        <Typography variant='body1'>Streak Rewards</Typography>
        <Typography variant='body1'>Log In Daily, Boost Your Free SC & GC!</Typography>
        <Box className='progress-main-wrap'>
          <Box className='progress-wrap'>
            <Box className='progress-bar'>
              <Stack spacing={2} sx={{ flex: 1 }}>
                <LinearProgress variant='determinate' value={progressBar} />
              </Stack>
            </Box>
            <Box className='progress-content'>
              <Box className='content-box'>
                <img src={Day1} alt='day-1' />
                <Box className='label-dot'>1 </Box>
              </Box>
              <Box className='content-box'>
                <img src={Day3} alt='day-1' />
                <Box className='label-dot'>3 </Box>
              </Box>
              <Box className='content-box'>
                <img src={Day5} alt='day-1' />
                <Box className='label-dot'>5 </Box>
              </Box>
              <Box className='content-box'>
                <img src={Day7} alt='day-1' />
                <Box className='label-dot'>7 </Box>
              </Box>
            </Box>
          </Box>
        </Box>
        <Grid container spacing={{ xs: 1, md: 1 }}>
          {data &&
            data?.map((data, index) => {
              return (
                <Fragment key={data?.day}>
                  <DayBox data={data} enableAnimation={enableAnimation} claimDailyBonus={claimDailyBonus} />
                </Fragment>
              )
            })}
        </Grid>
        <Box className='claim-btn'>
          <Button
            className='btn btn-secondary'
            disabled={remainingTime !== '00:00:00'}
            onClick={() => claimDailyBonus()}
          >
            Claim Now!
            {isLoading ? (
              <CircularProgress />
            ) : ''}
          </Button>
        </Box>
      </DialogContent>
    </Grid>
  )
}
export default DailyBonus
