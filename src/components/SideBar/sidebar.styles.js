import { makeStyles } from '@mui/styles'

import { BonusHeading, Bonuspara, ButtonPrimary } from '../../../src/MainPage.styles'
import { BonusBgImage } from '../../components/ui-kit/icons/banner'

export default makeStyles((theme) => ({
  leftSearchModal: {
    [theme.breakpoints.down('md')]: {
      marginTop: theme.spacing(3.75)
    },
    '& .MuiInputBase-formControl': {
      borderRadius: `${theme.spacing(0.625)} !important`,
      padding: `${theme.spacing(0)} !important`,
      '&:focus': {
        padding: `${theme.spacing(0)} !important`,
        borderRadius: `${theme.spacing(0.625)} !important`
      }
    },
    '& .MuiTextField-root': {
      padding: `${theme.spacing(0, 0.94)} !important`,
      width: 'auto !important',
      [theme.breakpoints.down('md')]: {
        width: 'calc(100% - 30px) !important'
      },
      marginBottom: '0 !important',
      '& input': {
        padding: '8.5px 14px 8.5px 40px !important',
        '&:focus': {
          borderRadius: `${theme.spacing(0.625)} !important`
        }
      }
    },
    '& .search-icon': {
      left: `${theme.spacing(1.9)} !important`
    }
  },

  btnPrimary: {
    transition: 'all 0.3s ease 0s',
    background: theme.colors.primaryGradient,
    '&.MuiButton-root': {
      padding: theme.spacing(0.125),
      borderRadius: '30px',
      overflow: 'hidden',
      minWidth: '30px',
      textTransform: 'capitalize',
      fontWeight: theme.typography.fontWeightSemiBold
    },
    '& .btn-primary-content': {
      background: theme.colors.primaryBtnBg,
      width: '100%',
      height: '100%',
      padding: theme.spacing(0.3, 1.5),
      borderRadius: '30px',
      justifyContent: 'center',
      display: 'flex',
      [theme.breakpoints.down('sm')]: {
        fontSize: theme.spacing(0.75),
        padding: theme.spacing(0.3, 1)
      },
      '&:hover': {
        background: theme.colors.primaryGradient
      }
    },
    '&:hover': {
      '&.MuiButton-root': {
        background: theme.colors.primaryGradient
      }
    }
  },
  btnWhiteGradient: {
    transition: 'all 0.3s ease 0s',
    width: '100%',
    '& .btn-gradient': {
      '&.MuiButtonBase-root': {
        background: theme.colors.btnSecondaryBg,
        boxShadow: theme.shadows[1],
        borderRadius: '30px',
        color: theme.colors.authCardBg,
        position: 'relative',
        overflow: 'hidden',
        minHeight: '30px',
        width: '100%',
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75)
        },
        '&:before': {
          position: 'absolute',
          width: '700px',
          height: '100%',
          content: "''",
          backgroundImage: theme.colors.btnSecondryStrip,
          backgroundSize: '1rem 1rem'
        },
        '& span': {
          position: 'relative',
          color: theme.colors.authCardBg,
          zIndex: '2',
          fontWeight: theme.typography.fontWeightSemiBold,
          fontSize: theme.spacing(0.875)
        },
        '&:hover': {
          background: theme.colors.primaryGradient,
          color: theme.colors.white,
          borderColor: theme.colors.secondryBtnBorder,
          '& span': {
            color: theme.colors.white
          }
        }
      }
    }
  },
  btnGradientWrap: {
    width: '100%',
    marginRight: theme.spacing(0.313),

    '& .btn-gradient': {
      '&.MuiButtonBase-root': {
        background: theme.colors.primaryGradient,
        boxShadow: theme.shadows[1],
        borderRadius: '30px',
        minHeight: '30px',
        color: theme.colors.white,
        position: 'relative',
        overflow: 'hidden',
        width: '100%',
        [theme.breakpoints.down('sm')]: {
          fontSize: theme.spacing(0.75)
        },
        '&:before': {
          position: 'absolute',
          width: '700px',
          height: '100%',
          content: "''",
          backgroundImage: theme.colors.btnSecondryStrip,
          backgroundSize: '1rem 1rem'
        },
        '& .btn-span': {
          position: 'relative',
          color: theme.colors.white,
          zIndex: '2',
          fontWeight: theme.typography.fontWeightExtraBold,
          display: 'flex',
          fontSize: theme.spacing(0.875)
        },
        '&:hover': {
          backgroundColor: theme.colors.primaryGradient,
          color: theme.colors.white,
          borderColor: theme.colors.secondryBtnBorder
        }
      }
    }
  },
  lobbyWrap: {
    display: 'flex',
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      display: 'block'
    }
  },

  sidebarWrap: {
    '& .sidebar': {
      background: theme.colors.sidebarBg,
      // padding: theme.spacing(1, 0.625, 3.938),
      borderRadius: '0',
      width: '250px',
      position: 'fixed',
      top: '0',
      height: '100%',
      // overflowX: "hidden",
      // overflowY: "auto",
      [theme.breakpoints.down('lg')]: {
        position: 'fixed',
        left: '0',
        top: '0',
        transform: 'translateX(-100%)',
        zIndex: '9',
        borderRadius: '0',
        transition: '0.5s ease-in-out'
      },
      '& .sidebar-close-btn': {
        position: 'absolute',
        right: theme.spacing(1),
        top: theme.spacing(1),
        zIndex: '1',
        display: 'none',
        [theme.breakpoints.down('md')]: {
          display: 'block'
        },
        '& img': {
          width: theme.spacing(1),
          height: theme.spacing(1)
        }
      },
      '& .brand-logo': {
        cursor: 'pointer',
        padding: theme.spacing(1),
        '& a': {
          display: 'block',
          textAlign: 'center',
          [theme.breakpoints.down('lg')]: {
            display: 'none'
          },
          '& img': {
            width: '170px',
            maxHeight: '100px'
            // filter: 'drop-shadow(0px 7px 10px #02070a80)',
          }
        }
      },
      '& .auth-btn-wrap': {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        [theme.breakpoints.down('md')]: {
          margin: theme.spacing(0, 313)
        },

        '& .MuiButtonBase-root': {
          margin: theme.spacing(0.625, 0),
          width: '100%',
          fontSize: theme.spacing(0.75),
          lineHeight: 1.6,
          minHeight: '33px',
          marginRight: theme.spacing(0.625),
          '& span': {
            display: 'flex',
            '& img': {
              marginRight: theme.spacing(0.313)
            }
          }
        }
      },

      '& .sidebarscroll': {
        overflowY: 'auto',
        height: 'calc(100vh - 130px)',
        [theme.breakpoints.down('lg')]: {
          height: 'calc(100dvh - 120px)',
          marginTop: '20px'
        },
        padding: theme.spacing(0, 0.94),
        '&::-webkit-scrollbar': {
          width: '0'
        }
      },

      '& .profile-wrap': {
        margin: theme.spacing(1, 0),
        '& .profile-box': {
          display: 'flex',
          alignItems: 'center',
          gap: theme.spacing(0.75),
          marginTop: theme.spacing(2),
          '& .img-box': {
            width: theme.spacing(4),
            minWidth: theme.spacing(4),
            height: theme.spacing(4),
            border: `1px solid ${theme.colors.YellowishOrange}`,
            borderRadius: '50%',
            overflow: 'hidden',
            '& img': {
              width: '100%',
              height: '100%',
              objectFit: 'cover',
              objectPosition: 'center'
            }
          },
          '& .text-box': {
            width: '100%',
            display: 'flex',
            justifyContent: 'flex-start',
            alignItems: 'center',
            flexDirection: 'row',
            gap: theme.spacing(0.625),
            '& .username-text': {
              width: '100%',
              maxWidth: theme.spacing(6.5),
              fontSize: theme.spacing(1.25),
              fontWeight: 'bold',
              textAlign: 'left',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              overflow: 'hidden'
            },
            '& img': {
              width: theme.spacing(0.5),
              height: 'auto'
            }
          }
        },
        '& .sc-wrap': {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '0.5rem',
          padding: '0.75rem',
          background: '#0D0C0F',
          marginTop: '1rem',
          borderRadius: '2rem'
        },
        '& .level-card': {
          backgroundColor: '#000000',
          borderRadius: theme.spacing(0.625),
          overflow: 'hidden',
          margin: theme.spacing(1.25, 0),
          '& .level-head': {
            backgroundColor: '#0d0c0f',
            padding: theme.spacing(0.25, 0.75, 0.25, 0.25),
            '& .level-box': {
              display: 'flex',
              alignItems: 'center',
              gap: theme.spacing(1),
              '& .img-box': {
                width: theme.spacing(3),
                minWidth: theme.spacing(3),
                height: theme.spacing(3),
                overflow: 'hidden',
                '& img': {
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                  objectPosition: 'center'
                }
              },
              '& .text-wrap': {
                width: '100%',
                display: 'flex',
                alignItems: 'center',
                gap: theme.spacing(0.625),
                '& .text-box': {
                  width: '100%',
                  gap: theme.spacing(0),
                  '& .title-text': {
                    width: '100%',
                    maxWidth: theme.spacing(7.375),
                    fontSize: theme.spacing(1),
                    fontWeight: 'bold',
                    textAlign: 'left',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                    overflow: 'hidden'
                  },
                  '& .subtitle-text': {
                    width: '100%',
                    maxWidth: theme.spacing(7.375),
                    color: '#858585',
                    fontSize: theme.spacing(0.625),
                    fontWeight: 'bold',
                    textAlign: 'left',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis',
                    overflow: 'hidden'
                  },
                  '& img': {
                    width: theme.spacing(0.75),
                    height: 'auto'
                  }
                }
              }
            }
          },
          '& .level-body': {
            padding: theme.spacing(0.5),
            '& .heading-text': {
              fontSize: theme.spacing(0.813),
              fontWeight: 'bold',
              marginBottom: theme.spacing(0.25)
            },
            '& .user-achieved-content': {
              display: 'flex',
              alignItems: 'center',
              marginTop: theme.spacing(0.625),
              '& .MuiTypography-h4': {
                fontSize: theme.spacing(0.875),
                fontWeight: theme.typography.fontWeightBold,
                color: theme.colors.white,
                marginLeft: theme.spacing(2.313)
              }
            },
            '& .progress-bar-box': {
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginTop: theme.spacing(0.75),
              '& .img-box': {
                width: theme.spacing(1.5),
                minWidth: theme.spacing(1.5),
                height: theme.spacing(1.5),
                overflow: 'hidden',
                '& img': {
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain',
                  objectPosition: 'center'
                }
              },
              '& .MuiLinearProgress-root': {
                width: '100%',
                height: '5px',
                backgroundColor: '#2b2b2b',
                borderRadius: '2.5px',
                '& .MuiLinearProgress-bar': {
                  backgroundColor: '#FDB72E',
                  borderRadius: '2.5px'
                }
              }
            },
            '& .progress-bar-text-box': {
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              gap: theme.spacing(0.625),
              marginTop: theme.spacing(1),
              '& .text-1': {
                fontSize: theme.spacing(0.813),
                fontWeight: 'bold'
              },
              '& .text-2': {
                color: '#858585',
                fontSize: theme.spacing(0.813),
                fontWeight: 'bold'
              }
            }
          }
        }
      },

      '& .sidebar-nav-list-wrap': {
        padding: theme.spacing(0.62, 0.938),
        borderRadius: theme.spacing(0.625),
        border: `2px solid ${theme.colors.GreenishCyan}`,
        margin: theme.spacing(0.625, 0),
        '& a': {
          display: 'flex',
          alignItems: 'center',
          textDecoration: 'none',
          padding: theme.spacing(0.81, 1),
          fontSize: theme.spacing(0.875),
          fontWeight: theme.typography.fontWeightMedium,
          position: 'relative',
          borderRadius: theme.spacing(0.625),
          overflow: 'hidden',
          marginBottom: theme.spacing(0),
          cursor: 'pointer',
          color: theme.colors.textWhite,
          '& img': {
            marginRight: theme.spacing(0.625),
            width: '24px !important',
            height: 'auto'
          },
          '&:hover, &.active': {
            background: '#0C2320',
            boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.25)',
            borderRadius: theme.spacing(0.625),
            '& img': {
              filter: 'none'
            },
            '&:before': {
              display: 'block'
            }
          },
          '& .MuiTypography-body1': {
            fontWeight: '500',
            fontSize: theme.spacing(1)
          }
        }
      },

      '& .sidebar-accordian': {
        fontWeight: theme.typography.fontWeightMedium,
        '& .MuiPaper-elevation': {
          backgroundColor: 'transparent !important',
          backdropFilter: 'none !important'
        },
        '& .MuiPaper-root': {
          background: 'transparent',
          boxShadow: 'none',
          color: theme.colors.white
        },
        '& .MuiButtonBase-root': {
          padding: theme.spacing(0.625, 0.313),
          minHeight: 'auto',
          position: 'relative',
          overflow: 'hidden',
          '&:before': {
            position: 'absolute',
            left: '0',
            top: '0',
            backgroundSize: '100px',
            height: '100%',
            width: '100px',
            content: "''",
            backgroundRepeat: 'no-repeat',
            display: 'none'
          },
          '& .MuiAccordionSummary-content': {
            margin: '0'
          },
          '&.Mui-expanded': {
            backgroundColor: theme.colors.sidebarActive,
            padding: theme.spacing(0.625, 0.313),
            backdropFilter: 'blur(20px)',
            borderRadius: theme.spacing(0.625),
            marginBottom: theme.spacing(0.313),
            '&:before': {
              display: 'block'
            },
            '& .MuiTypography-body1': {
              '& img': {
                position: 'absolute',
                '&:nth-of-type(1)': {
                  display: 'none'
                },
                '&:nth-of-type(2)': {
                  display: 'block'
                }
              },
              '& span': {
                fontSize: theme.spacing(0.875),
                display: 'flex',
                fontWeight: theme.typography.fontWeightMedium,
                alignItems: 'center',
                marginLeft: theme.spacing(2),
                background: theme.colors.textGradient,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }
            }
          },
          '&:hover': {
            '& .MuiTypography-body1': {
              '& img': {
                position: 'absolute',
                '&:nth-of-type(1)': {
                  display: 'none'
                },
                '&:nth-of-type(2)': {
                  display: 'block'
                }
              },
              '& span': {
                background: theme.colors.textGradient,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }
            }
          }
        },
        '& .MuiSvgIcon-root': {
          color: theme.colors.accordianIcon
        },
        '& .MuiAccordionDetails-root': {
          padding: '0'
        },
        '& .MuiTypography-body1': {
          position: 'relative',
          '& img': {
            position: 'absolute',
            '&:nth-of-type(2)': {
              display: 'none'
            },
            '&:nth-of-type(1)': {
              display: 'block'
            }
          },
          '& span': {
            fontSize: theme.spacing(0.875),
            display: 'flex',
            fontWeight: theme.typography.fontWeightMedium,
            alignItems: 'center',
            marginLeft: theme.spacing(2)
          }
        }
      },
      '& .after-login-section': {
        background: theme.colors.sidebarNavBg,
        padding: theme.spacing(1.25, 0.938, 0.938, 0.938),
        borderRadius: theme.spacing(0.5),
        margin: theme.spacing(0.625, 0),
        '& .profile-pic': {
          height: '65px',
          width: '65px',
          borderRadius: '100%',
          overflow: 'hidden',
          flex: '0 0 65px',
          marginRight: theme.spacing(0.625),
          '& img': {
            width: '100%',
            height: '100%',
            objectFit: 'cover'
          }
        },
        '& .profile-details': {
          flexGrow: '1',
          cursor: 'pointer',
          '& .MuiTypography-body1': {
            textTransform: 'uppercase',
            '& span': {
              color: theme.colors.themeText
            }
          },
          '& .MuiTypography-h4': {
            color: theme.colors.white,
            fontSize: theme.spacing(1),
            fontWeight: theme.typography.fontWeightExtraBold,
            display: 'flex',
            alignItems: 'center',
            '& span': {
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              overflow: 'hidden',
              width: '100px'
            },
            '& a': {
              display: 'block',
              marginLeft: theme.spacing(0.625),
              '& img': {
                width: '15px',
                height: '15px'
              }
            }
          }
        },
        '& .user-balance-wrap': {
          display: 'flex',
          alignItems: 'center',
          marginTop: theme.spacing(0.313),
          '&  .MuiTypography-body1': {
            fontSize: theme.spacing(0.75),
            display: 'flex',
            alignItems: 'center',
            fontWeight: theme.typography.fontWeightBold,
            color: theme.colors.white,
            marginRight: theme.spacing(0.625),
            '& img': {
              marginRight: theme.spacing(0.2)
            }
          }
        },
        '& .after-login-top-content': {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          width: '100%'
        },
        '& .user-details-top': {
          display: 'flex',
          alignItems: 'center'
        },
        '& .get-coins-cta-wrap': {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          margin: theme.spacing(1, 0),
          '& .MuiButtonBase-root': {
            padding: theme.spacing(0.1, 0.875)
          }
        },
        '& .user-achievement-content': {
          background: theme.colors.userAchivementCardTopBg,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: theme.spacing(0.625),
          borderRadius: '10px 10px 0 0',
          color: theme.colors.white,
          cursor: 'pointer',
          '& .MuiTypography-h4': {
            fontSize: theme.spacing(0.75),
            fontWeight: theme.typography.fontWeightBold
          },
          '& .MuiTypography-body1': {
            fontSize: theme.spacing(0.75),
            color: theme.colors.themeText
          },
          '& .user-achievement-badge': {
            width: '40px',
            height: '40px'
          },
          '& .user-achievement-right': {
            flexGrow: '1',
            marginLeft: theme.spacing(0.313)
          }
        },
        '& .user-achievement-bottom-content': {
          background: theme.colors.black,
          alignItems: 'center',
          padding: theme.spacing(0.625),
          borderRadius: '0 0 10px 10px',
          '& .MuiTypography-h4': {
            fontSize: theme.spacing(0.75),
            fontWeight: theme.typography.fontWeightBold,
            color: theme.colors.white
          },

          '& .custom-progressbar': {
            margin: theme.spacing(1, 0, 0),
            position: 'relative',
            '& .MuiLinearProgress-colorPrimary': {
              background: theme.colors.authCardBg,
              height: '5px',
              borderRadius: '20px'
            },
            '& .MuiLinearProgress-bar': {
              background: theme.colors.primaryGradient,
              borderRadius: '20px',
              overflow: 'hidden',
              '&:before': {
                position: 'absolute',
                width: '100%',
                height: '100%',
                content: "''",
                backgroundImage: theme.colors.btnSecondryStrip,
                backgroundSize: '1rem 1rem'
              }
            },
            '& .custom-progressbar-value': {
              position: 'absolute',
              top: '10px',
              right: '10px',
              color: theme.colors.white
            },
            '& .progress-btn-wrap': {
              position: 'absolute',
              top: '-17px',
              left: '0',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              width: '100%',
              '& img': {
                width: theme.spacing(2.5),
                // height:theme.spacing(2.5),
                '&:first-child': {
                  marginLeft: '-10px'
                },
                '&:last-child': {
                  marginRight: '-10px'
                }
              }
            },
            '& .progress-bar-content': {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginTop: theme.spacing(1),
              '& .MuiTypography-body1': {
                fontSize: theme.spacing(0.875)
              },
              '& span': {
                color: theme.colors.themeText,
                fontSize: theme.spacing(0.875)
              }
            }
          }
        }
      },
      '&.sidebarActive': {
        [theme.breakpoints.down('lg')]: {
          transform: 'translateX(0px)',
          top: '0',
          background: theme.colors.mobSidebarBg
        }
      }
    }
    // '& .maintenance-bubble': {
    //   height: '60px',
    //   background: theme.colors.YellowishOrange,
    //   position: 'fixed',
    //   bottom: '50px',
    //   right: '50px',
    //   zIndex: '999',
    //   borderRadius: '70px',
    //   display: 'flex',
    //   alignItems: 'center',
    //   justifyContent: 'center',
    //   width: '60px',
    //   cursor: 'pointer',
    //   [theme.breakpoints.down('sm')]: {
    //     bottom: '70px',
    //     right: '30px',
    //     width: '45px',
    //     height: '45px',
    //     '& img': {
    //       width: '20px !important'
    //     }
    //   },
    //   '& img': {
    //     width: '30px'
    //   },
    //   '& .bubble-box': {
    //     padding: '0.5rem 0.75rem',
    //     borderRadius: '0.5rem',
    //     border: '1px solid #FDB72E',
    //     textAlign: 'center',
    //     background: 'rgb(27, 24, 30)',
    //     position: 'absolute',
    //     transition: 'all 0.2s ease-in-out',
    //     top: '-8.5rem',
    //     right: '-25rem',
    //     display: 'flex',
    //     flexDirection: 'column',
    //     gap: '0.5rem',
    //     minWidth: '250px',
    //     '&.bubble-open': {
    //       right: '0rem'
    //     },
    //     '& p': {
    //       fontSize: '1rem',
    //       fontWeight: '600',
    //       color: theme.colors.textWhite
    //     },
    //     '& .maintenancetimer': {
    //       display: 'flex',
    //       // marginTop: '1rem',
    //       alignItems: 'center',
    //       textAlign: 'center',
    //       // gap: theme.spacing(1),
    //       fontWeight: theme.typography.fontWeightBold,
    //       [theme.breakpoints.down(1300)]: {
    //         // gap: theme.spacing(0.5)
    //       },
    //       '& .counter-divider': {
    //         // display: 'none',
    //         fontSize: theme.spacing(2),
    //         padding: theme.spacing(0, 0.313),
    //         color: '#AAA9A9',
    //         marginTop: theme.spacing(-1.5)
    //       },
    //       '& .maintenanceCountdown': {
    //         textAlign: 'center',
    //         display: 'flex',
    //         flexDirection: 'column',
    //         background: 'linear-gradient(176.23deg, #cccccc 25.71%, #656464 96.95%)',
    //         borderRadius: '10px',
    //         padding: '1.5px',
    //         boxShadow: '0px 0px 8.19px 0px #CCCCCC',
    //         marginBottom: '0.25rem'

    //         // gap: theme.spacing(0.313),
    //         // gap: theme.spacing(0.313),
    //       },
    //       '& .MuiTypography-span': {
    //         fontSize: theme.spacing(0.875),
    //         padding: '2px 0',
    //         color: '#949494'
    //       },
    //       '& .maintenanceTime': {
    //         // background: theme.colors.counterBg,
    //         color: theme.colors.textWhite,
    //         fontSize: theme.spacing(1.25),
    //         fontWeight: '700',
    //         padding: '12px 16px',
    //         textAlign: 'center',
    //         // fontStyle: 'italic',
    //         // transform: 'skewX(-20deg)',
    //         // boxShadow: theme.shadows[15],
    //         minWidth: theme.spacing(3),
    //         borderRadius: '10px',
    //         // borderBottomRightRadius: '0',
    //         background: '#313131',
    //         // borderBottomLeftRadius: '0',
    //         // minHeight: theme.spacing(2.6969),

    //         // [theme.breakpoints.down('xl')]: {
    //         //   fontSize: theme.spacing(1.8),
    //         //   minWidth: theme.spacing(5.5)
    //         // },
    //         [theme.breakpoints.down(1300)]: {
    //           fontSize: theme.spacing(1.5),
    //           // minWidth: theme.spacing(3.5),
    //           padding: '12px'
    //         },
    //         [theme.breakpoints.down('sm')]: {
    //           fontSize: theme.spacing(1.25)
    //           // minWidth: theme.spacing(3.25)
    //         }
    //       }
    //     }
    //   }
    // }
  },
  lobbySearchWrap: {
    position: 'relative',
    '& .search-icon': {
      position: 'absolute',
      top: theme.spacing(1.938),
      left: theme.spacing(1.25)
    },
    '& .MuiTextField-root': {
      width: '100%',
      margin: '20px 0',
      '& .MuiInputBase-formControl': {
        width: '100%',
        borderRadius: theme.spacing(0.625),
        border: `2px solid ${theme.colors.GreenishCyan}`,
        '& fieldset': {
          border: '0'
        }
      },
      '& input': {
        padding: '8.5px 14px 8.5px 60px',
        color: theme.colors.textWhite,
        '&::placeholder': {
          color: 'red'
        },
        '&::-ms-input-placeholder': {
          color: 'red'
        },
        '&:focus': {
          borderColor: 'rgb(255, 255, 193)',
          boxShadow: 'rgb(255, 252, 57) 0px 0px 1px inset, rgb(255, 93, 0) 0px 0px 4px',
          borderRadius: theme.spacing(0.625)
        }
      }
    }
  },
  // Rward Modal Css start from here
  rewardStepModal: {
    '& .MuiPaper-root': {
      minWidth: '1000px',
      overflowY: 'visible',
      [theme.breakpoints.down('md')]: {
        minWidth: '95%'
      },
      background: theme.colors.primaryGradient,
      '& .reward-modal-content': {
        position: 'relative',
        overflowY: 'visible',
        '& .modal-close': {
          position: 'absolute',
          right: '10px',
          zIndex: '2',
          '& .MuiButtonBase-root': {
            background: theme.colors.white,
            padding: '0.625rem',
            borderRadius: theme.spacing(0.625),
            height: theme.spacing(2),
            width: theme.spacing(2),
            color: theme.colors.themeText,
            [theme.breakpoints.down('sm')]: {
              marginRight: '0'
            },
            '& svg': {
              color: theme.colors.black,
              fontSize: theme.spacing(0.875)
            },
            '&:hover': {
              '& svg': {
                color: theme.colors.highlighColor
              }
            }
          }
        },
        '& .reward-modal-top-img': {
          textAlign: 'center',
          position: 'relative',
          '& > img': {
            width: '480px',
            margin: '-120px auto 0',
            [theme.breakpoints.down('md')]: {
              width: '300px'
            },
            [theme.breakpoints.down('sm')]: {
              width: '100%'
            }
          },
          '& .reward-modal-top-content': {
            position: 'absolute',
            top: '47%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            [theme.breakpoints.down('md')]: {
              top: '28%'
            },
            '& .MuiTypography-h5': {
              color: theme.colors.black,
              fontSize: theme.spacing(1.2),
              fontWeight: theme.typography.fontWeightBold,
              textTransform: 'capitalize',
              margin: '0',
              lineHeight: '1',
              [theme.breakpoints.down('md')]: {
                fontSize: theme.spacing(1)
              }
            },
            '& .MuiTypography-h4': {
              color: theme.colors.black,
              fontSize: theme.spacing(2),
              fontWeight: theme.typography.fontWeightBold,
              textTransform: 'capitalize',
              margin: '0',
              lineHeight: '1.1',
              [theme.breakpoints.down('md')]: {
                fontSize: theme.spacing(1.1)
              }
            },
            '& .MuiTypography-body1': {
              fontSize: theme.spacing(1),
              color: theme.colors.black,
              textTransform: 'capitalize',
              margin: '0',
              lineHeight: '1',
              fontWeight: theme.typography.fontWeightMedium,
              [theme.breakpoints.down('md')]: {
                fontSize: theme.spacing(0.625)
              }
            }
          }
        },
        '& .reward-steps-wrap': {
          [theme.breakpoints.down('lg')]: {
            whiteSpace: 'nowrap',
            overflowX: 'auto'
          },
          '& .reward-steps-day-count': {
            display: 'grid',
            gridGap: 0,
            gridTemplateColumns: ' repeat(7, 1fr)',
            '& .MuiLink-root': {
              textDecoration: 'none',
              background: theme.colors.authCardBg,
              borderWidth: '0 1px 0 0',
              borderStyle: 'solid',
              borderColor: theme.colors.themeText,
              padding: theme.spacing(0.625, 2),
              textAlign: 'center',
              position: 'relative',
              [theme.breakpoints.down('md')]: {
                minWidth: '62px'
              },
              [theme.breakpoints.down('sm')]: {
                minWidth: '60px'
              },
              '&:first-child': {
                borderTopLeftRadius: theme.spacing(0.313),
                borderBottomLeftRadius: theme.spacing(0.313)
              },
              '&:last-child': {
                borderTopRightRadius: theme.spacing(0.313),
                borderBottomRightRadius: theme.spacing(0.313)
              },
              '&:before': {
                content: "''",
                width: '0',
                height: '0',
                position: 'absolute',
                bottom: '-15px',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                borderLeft: '10px solid transparent',
                borderRight: '10px solid transparent',
                borderTopColor: theme.colors.black,
                borderTopStyle: 'solid',
                borderTopWidth: '10px',
                display: 'none'
              },
              '&.active': {
                background: theme.colors.white,
                color: theme.colors.authCardBg,
                fontWeight: theme.typography.fontWeightBold,
                '&:before': {
                  display: 'block'
                }
              }
            }
          },
          '& .reward-steps-day-claim': {
            margin: theme.spacing(1.2, 0),
            display: 'grid',
            gridGap: theme.spacing(0.625),
            gridTemplateColumns: ' repeat(7, 1fr)',
            '& .reward-claim-card': {
              background: theme.colors.black,
              borderRadius: '20px',
              padding: theme.spacing(1),
              filter: 'grayscale(10)',
              opacity: '0.5',
              [theme.breakpoints.down('sm')]: {
                minWidth: '115px'
              },
              '&.active': {
                filter: 'none',
                opacity: '1'
              },
              '& .clam-icon': {
                textAlign: 'center',
                '& img': {
                  width: '100%',
                  height: '60px',
                  margin: '0.625rem auto'
                }
              },
              '& .clam-coins-details': {
                marginBottom: theme.spacing(0.625),
                '& .MuiTypography-body1': {
                  display: 'flex',
                  alignItems: 'center',
                  fontSize: theme.spacing(0.75),
                  fontWeight: theme.typography.fontWeightMedium,
                  '& img': {
                    width: theme.spacing(1),
                    marginRight: theme.spacing(0.313)
                  }
                }
              },
              '& .btn-gradient': {
                minHeight: '37px'
              }
            }
          }
        }
      }
    }
  },

  leftSidebarSearch: {
    position: 'relative',
    height: '40px',
    marginBottom: '10px',
    '& input': {
      borderRadius: theme.spacing(0.625),
      background: theme.colors.inputColor,
      paddingLeft: theme.spacing(2.8),
      color: theme.colors.textWhite,
      height: '100%'
    },
    '& .MuiTextField-root': {
      width: '100%'
    },
    '& fieldset': {
      border: 'none'
    }
  },

  searchIcon: {
    position: 'absolute',
    top: '17px',
    left: '10px',
    color: theme.colors.textWhite
  },

  dailyBonusBg: {
    '& .coinSection': {
      display: 'flex',
      alignItems: 'center',
      gap: '10px',
      '& p': {
        margin: '0',
        display: 'flex',
        alignItems: 'center',
        marginTop: '10px',
        marginBottom: '10px',
        fontWeight: 'bold',
        fontSize: theme.spacing(1.5),
        color: theme.colors.YellowishOrange,
        gap: '10px'
      }
    },
    backgroundImage: `url(${BonusBgImage})`,
    '& h2': {
      ...BonusHeading(theme),
      textTransform: 'uppercase'
    },
    '& p': {
      ...Bonuspara(theme)
    },
    '& .claimBtn': {
      ...ButtonPrimary(theme),
      '&:hover': {
        ...ButtonPrimary(theme)
      },
      marginTop: '15px'
    },
    '& .modal-close': {
      color: theme.colors.textWhite,
      textAlign: 'right'
    }
  }
}))
