import React, { useRef, useEffect, useState, useCallback, useMemo, memo, lazy, Suspense } from 'react'
import useStyles from './sidebar.styles'
import '../../../src/App.css'
import { Grid, Link, Typography, Box, LinearProgress, Skeleton } from '@mui/material'
import sidebarCross from '../ui-kit/icons/svg/sidebar-cross.svg'
import { BrandLogo, BrandLogoMob } from '../ui-kit/icons/brand'
import {
  Recommendation,
  RedeemPoints,
  Support,
  Tournament,
  SettingsIcon,
  VaultIcon,
  hallofFame,
  caretRight,
  multiColorPromotion,
  affiliateNew,
  multiColorRecent,
  multiColorLogout,
  multiColorFavorite,
  multiColorInvoice,
  multiColorProfile,
  JackpotIcon
} from '../ui-kit/icons/svg'
import redeemableSC from '../../components/ui-kit/icons/png/redeemable-sc.png'
import { useLogOutMutation } from '../../reactQuery'
import { useNavigate, useLocation } from 'react-router-dom'
import { getLoginToken } from '../../utils/storageUtils'
import { useUserStore } from '../../store/useUserSlice'
import { PlayerRoutes } from '../../routes'
import { usePortalStore } from '../../store/userPortalSlice'
import { SubCategoryConstants } from './constants'
import { useGamesStore, useSiteLogoStore } from '../../store/store'
import useLogout from '../../hooks/useLogout'
import useIntercom from './hooks/useIntercom'
import TagManager from 'react-gtm-module'
import { formatPriceWithCommas } from '../../utils/helpers'
import LazyImage from '../../utils/lazyImage'
import ImageRenderer from '../ImageRenderer'
// Lazy load heavy components for better performance
const SubCategories = lazy(() => import('./components/Subcategories'))
const MobNavbar = lazy(() => import('../MobNavbar'))
const Vault = lazy(() => import('../../pages/Vault'))
const StepperForm = lazy(() => import('../StepperForm'))

// Memoized loading skeleton
const SidebarSkeleton = memo(() => (
  <Box sx={{ padding: '1rem' }}>
    <Skeleton variant='rectangular' height={60} sx={{ marginBottom: '1rem', borderRadius: '8px' }} />
    <Skeleton variant='rectangular' height={40} sx={{ marginBottom: '0.5rem', borderRadius: '4px' }} />
    <Skeleton variant='rectangular' height={40} sx={{ marginBottom: '0.5rem', borderRadius: '4px' }} />
    <Skeleton variant='rectangular' height={40} sx={{ marginBottom: '0.5rem', borderRadius: '4px' }} />
  </Box>
))

// Memoized profile section
const ProfileSection = memo(({ userDetails, handleAccount, handleTier, formatPriceWithCommas }) => (
  <Box className='profile-wrap'>
    <Link onClick={handleAccount} style={{ color: '#FFFFFF', textDecoration: 'none', cursor: 'pointer' }}>
      <Box className='profile-box'>
        <Box className='img-box'>
          <LazyImage
            src={userDetails?.profileImage || BrandLogoMob}
            alt='Profile image'
            aspectRatio='1/1'
            borderRadius='50%'
            sizes='40px'
            priority
            style={{ width: '100%', height: '100%' }}
          />
        </Box>
        <Box className='text-box'>
          <Typography className='username-text'>{userDetails?.username}</Typography>
          <img loading='lazy' src={caretRight} alt='caretRight' />
        </Box>
      </Box>
    </Link>
    <Box className='sc-wrap'>
      <LazyImage
        src={redeemableSC}
        alt='redeemable-sc-coin'
        priority
        sizes='21px'
        style={{ width: '21px', height: '21px' }}
      />
      <Typography
        sx={{
          fontSize: '14px',
          color: '#858585',
          fontWeight: 700
        }}
      >
        Redeemable SC -{' '}
        <Typography variant='span' style={{ color: '#FDB72E' }}>
          {formatPriceWithCommas(userDetails?.userWallet?.scCoin?.wsc)}
        </Typography>
      </Typography>
    </Box>
    <Link onClick={handleTier} style={{ color: '#FFFFFF', textDecoration: 'none', cursor: 'pointer' }}>
      <TierCard userDetails={userDetails} />
    </Link>
  </Box>
))

// Memoized tier card component
const TierCard = memo(({ userDetails }) => (
  <Box className='level-card'>
    <Box className='level-head'>
      <Box className='level-box'>
        <Box className='img-box'>
          <LazyImage
            src={userDetails?.tierDetail?.currentTier?.icon}
            alt='Current tier icon'
            aspectRatio='1/1'
            sizes='40px'
            style={{ width: '100%', height: '100%' }}
          />
        </Box>
        <Box className='text-wrap'>
          <Box className='text-box'>
            <Typography className='title-text'>{userDetails?.tierDetail?.currentTier?.name}</Typography>
            <Typography className='subtitle-text'>Your Loyalty Level</Typography>
          </Box>
          <img loading='lazy' src={caretRight} alt='caretRight' />
        </Box>
      </Box>
    </Box>
    {userDetails?.tierDetail?.isMaxTier ? (
      <Box className='level-body'>
        <Grid className='user-achieved-content'>
          <Typography variant='h4'> Max VIP Level Achieved</Typography>
        </Grid>
      </Box>
    ) : (
      <Box className='level-body'>
        <Typography className='heading-text'>Monthly Loyalty Progress</Typography>
        <Box className='progress-bar-box'>
          <Box className='img-box'>
            <LazyImage
              src={userDetails?.tierDetail?.currentTier?.icon}
              alt='Current tier'
              aspectRatio='1/1'
              sizes='24px'
              style={{ width: '100%', height: '100%' }}
            />
          </Box>
          <LinearProgress variant='determinate' value={userDetails?.tierDetail?.percentage || 0} />
          <Box className='img-box'>
            <LazyImage
              src={userDetails?.tierDetail?.nextTier?.icon}
              alt='Next tier'
              aspectRatio='1/1'
              sizes='24px'
              style={{ width: '100%', height: '100%' }}
            />
          </Box>
        </Box>
        <Box className='progress-bar-text-box'>
          <Typography className='text-1'>{userDetails?.tierDetail?.currentTier?.name}</Typography>
          <Typography className='text-2'>{userDetails?.tierDetail?.currentXp} XP</Typography>
          <Typography className='text-1'>{userDetails?.tierDetail?.nextTier?.name}</Typography>
        </Box>
      </Box>
    )}
  </Box>
))

// Memoized navigation item
const NavItem = memo(({ onClick, isActive, icon, label, className = '' }) => (
  <Link onClick={onClick} className={isActive ? 'active' : 'Inactive'}>
    <LazyImage src={icon} alt={label} sizes='24px' style={{ width: '100%', height: '100%' }} className={className} />
    <Typography>{label}</Typography>
  </Link>
))

const SideBar = () => {
  useIntercom(true)
  const [isMobNav, setIsMobNav] = useState(false)
  const sidebarRef = useRef(null)

  // Store selectors
  const setSelectedSubCat = useGamesStore((state) => state.setSelectedSubCat)
  const selectedSubCat = useGamesStore((state) => state.selectedSubCat)
  const setIsAuthenticate = useUserStore((state) => state.setIsAuthenticate)
  const userStore = useUserStore((state) => state)
  const userDetails = useUserStore((state) => state.userDetails)
  const auth = useUserStore((state) => state.isAuthenticate)
  const portalStore = usePortalStore((state) => state)
  const logoData = useSiteLogoStore((state) => state)

  // Hooks
  const navigate = useNavigate()
  const location = useLocation()
  const classes = useStyles()
  const { logoutHandler } = useLogout()

  // Memoized authentication check
  const isAuthenticated = useMemo(() => !!getLoginToken())
  console.log(isAuthenticated, 'Sidebar auth State', !!getLoginToken(), 'tee', auth)

  // Memoized modal close handler
  const handleClose = useCallback(() => {
    setOpen(false)
  }, [])

  // Memoized navigation handlers
  const handleLogout = useCallback(() => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'logout',
        email: userDetails?.email,
        user_id: userDetails?.userId
      }
    })
    setIsAuthenticate(false)
    mutation.mutate()
    userStore.logout()
  }, [userDetails?.email, userDetails?.userId, setIsAuthenticate, userStore])

  const handleOpenWithdraw = useCallback(() => {
    portalStore.openPortal(
      () => (
        <Suspense fallback={<div>Loading...</div>}>
          <StepperForm stepperCalledFor={'redeem'} />
        </Suspense>
      ),
      'StepperModal'
    )
  }, [portalStore])

  const handleOpenVault = useCallback(() => {
    portalStore.openPortal(
      () => (
        <Suspense fallback={<div>Loading...</div>}>
          <Vault />
        </Suspense>
      ),
      'valutModal'
    )
  }, [portalStore])

  const handlelobby = useCallback(() => {
    setIsMobNav(false)
    navigate('/')
    setSelectedSubCat('Lobby')
  }, [navigate, setSelectedSubCat])

  const handlePromotion = useCallback(() => {
    setIsMobNav(false)
    navigate(PlayerRoutes.PromotionsPage)
  }, [navigate])

  const handleTournaments = useCallback(() => {
    setIsMobNav(false)
    navigate(PlayerRoutes.TournamentsPage)
  }, [navigate])

  const handleSettings = useCallback(() => {
    setIsMobNav(false)
    navigate(PlayerRoutes.Settings)
  }, [navigate])

  const handleJackpot = useCallback(() => {
    setIsMobNav(false)
    navigate(PlayerRoutes.Jackpot)
  }, [navigate])

  const handleTier = useCallback(() => {
    setIsMobNav(false)
    navigate(PlayerRoutes.Tier)
  }, [navigate])

  const handleAccount = useCallback(() => {
    setSelectedSubCat(null)
    setIsMobNav(false)
    navigate(PlayerRoutes.Account)
  }, [navigate, setSelectedSubCat])

  const handleAffiliate = useCallback(() => {
    setIsMobNav(false)
    navigate(PlayerRoutes.Affiliate)
  }, [navigate])

  const handleReferPage = useCallback(() => {
    setIsMobNav(false)
    navigate(PlayerRoutes.ReferPage)
  }, [navigate])

  const handleHallOfFame = useCallback(() => {
    setIsMobNav(false)
    navigate(PlayerRoutes.HallOfFame)
  }, [navigate])

  const handleRecentlyPlayed = useCallback(() => {
    setIsMobNav(false)
    navigate('/')
    setSelectedSubCat(SubCategoryConstants.RECENTLY_PLAYED)
  }, [navigate, setSelectedSubCat])

  const handleFavoriteGames = useCallback(() => {
    setIsMobNav(false)
    navigate('/')
    setSelectedSubCat(SubCategoryConstants.FAVORITE_GAMES)
  }, [navigate, setSelectedSubCat])

  const handleBets = useCallback(() => {
    setIsMobNav(false)
    navigate(PlayerRoutes.Bets)
    setSelectedSubCat('')
  }, [navigate, setSelectedSubCat])

  const handleOpenSupport = useCallback(() => {
    setIsMobNav(false)
    window.Intercom('show')
  }, [])

  // Memoized mutation
  const mutation = useLogOutMutation({
    onSuccess: () => {
      logoutHandler()
    },
    onError: (error) => {
      console.error('Logout error:', error)
    }
  })
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isMobNav && sidebarRef.current && !sidebarRef.current.contains(event.target)) {
        setIsMobNav(false)
      }
    }

    document.addEventListener('click', handleClickOutside)

    return () => {
      document.removeEventListener('click', handleClickOutside)
    }
  }, [isMobNav])

  useEffect(() => {
    if (!!getLoginToken() && auth && userDetails) {
      window.Intercom('boot', {
        app_id: 'truve12n',
        name: userDetails?.username,
        email: userDetails?.email,
        user_id: userDetails?.userId
      })
    } else {
      window.Intercom('shutdown')
      window.Intercom('update', {
        hide_default_launcher: true
      })
    }
  }, [auth, userDetails])

  return (
    <div ref={sidebarRef}>
      <Grid className={classes.sidebarWrap}>
        <Grid className={isMobNav ? 'sidebar sidebarActive' : 'sidebar'}>
          <Link
            className='sidebar-close-btn'
            onClick={() => {
              setIsMobNav(false)
            }}
          >
            <ImageRenderer src={sidebarCross} alt='Cross' />
          </Link>
          <Grid className='brand-logo'>
            <Link onClick={handlelobby}>
              <LazyImage
                src={isMobNav ? logoData?.mobileLogo || BrandLogoMob : logoData?.desktopLogo || BrandLogo}
                alt={isMobNav ? 'The Money Factory mobile brand logo' : 'The Money Factory brand logo'}
                priority
                sizes={isMobNav ? '150px' : '200px'}
                style={{ width: '100%', height: '100%' }}
              />
            </Link>
          </Grid>

          <Grid className='sidebarscroll'>
            {isAuthenticated && (
              <ProfileSection
                userDetails={userDetails}
                handleAccount={handleAccount}
                handleTier={handleTier}
                formatPriceWithCommas={formatPriceWithCommas}
              />
            )}

            {isAuthenticated && (
              <Grid className='sidebar-nav-list-wrap'>
                <NavItem
                  onClick={handleFavoriteGames}
                  isActive={selectedSubCat === SubCategoryConstants.FAVORITE_GAMES}
                  icon={multiColorFavorite}
                  label='Favorites'
                />
                <NavItem
                  onClick={handleRecentlyPlayed}
                  isActive={selectedSubCat === SubCategoryConstants.RECENTLY_PLAYED}
                  icon={multiColorRecent}
                  label='Recent'
                />
                <NavItem
                  onClick={handleBets}
                  isActive={location?.pathname === '/bets'}
                  icon={multiColorInvoice}
                  label='My Plays'
                />
              </Grid>
            )}

            <Grid className='sidebar-nav-list-wrap'>
              <Suspense fallback={<SidebarSkeleton />}>
                <SubCategories setIsMobNav={setIsMobNav} />
              </Suspense>
            </Grid>

            <Grid className='sidebar-nav-list-wrap'>
              {isAuthenticated && (
                <NavItem
                  onClick={handleAccount}
                  isActive={location?.pathname === '/user/account-details'}
                  icon={multiColorProfile}
                  label='Profile'
                />
              )}
              {isAuthenticated && (
                <NavItem onClick={handleOpenWithdraw} isActive={false} icon={RedeemPoints} label='Redeem' />
              )}
              {isAuthenticated && <NavItem onClick={handleOpenVault} isActive={false} icon={VaultIcon} label='Vault' />}
              <NavItem
                onClick={handleAffiliate}
                isActive={location?.pathname === '/affiliate'}
                icon={affiliateNew}
                label='Affiliate'
              />
              <NavItem
                onClick={handlePromotion}
                isActive={location?.pathname === '/promotions-page'}
                icon={multiColorPromotion}
                label='Promotions'
              />

              {isAuthenticated && (
                <NavItem
                  onClick={handleTournaments}
                  isActive={
                    location?.pathname === '/tournaments-page' || location?.pathname.includes('/tournament-detail')
                  }
                  icon={Tournament}
                  label='Tournaments'
                />
              )}
              {isAuthenticated && (
                <NavItem
                  onClick={handleJackpot}
                  isActive={location?.pathname === '/jackpot'}
                  icon={JackpotIcon}
                  label='Jackpot'
                />
              )}
              {isAuthenticated && (
                <NavItem
                  onClick={handleSettings}
                  isActive={location?.pathname === '/settings'}
                  icon={SettingsIcon}
                  label='Settings'
                />
              )}
              {isAuthenticated &&
                userDetails?.isReferralBonusAllowed !== null &&
                userDetails?.isReferralBonusAllowed && (
                  <NavItem
                    onClick={handleReferPage}
                    isActive={location?.pathname === '/refer-a-friend'}
                    icon={Recommendation}
                    label='Refer a Friend'
                  />
                )}

              <NavItem
                onClick={handleHallOfFame}
                isActive={location?.pathname === '/hall-of-fame'}
                icon={hallofFame}
                label='Hall of Fame'
              />

              {import.meta.env.VITE_NODE_ENV === 'production' && (
                <NavItem onClick={handleOpenSupport} isActive={false} icon={Support} label='Live Chat' />
              )}

              {isAuthenticated && (
                <NavItem
                  onClick={handleLogout}
                  isActive={false}
                  icon={multiColorLogout}
                  label='Logout'
                  className='logout'
                />
              )}
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      <Suspense fallback={null}>
        <MobNavbar isMobNav={isMobNav} setIsMobNav={setIsMobNav} />
      </Suspense>
    </div>
  )
}

export default SideBar
