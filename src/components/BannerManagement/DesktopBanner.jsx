import { lazy, Suspense } from 'react'
import { Box, Grid, Typography } from '@mui/material'
import { getLoginToken } from '../../utils/storageUtils'
import { useUserStore } from '../../store/useUserSlice'
import { useNavigate } from 'react-router-dom'
import { usePortalStore } from '../../store/store'
import LazyImage from '../../utils/lazyImage'

const Signup = lazy(() => import('../Modal/Signup'))
const CountDownTimer = lazy(() => import('../CountDownTimer'))

const DesktopBanner = ({ info, index }) => {
  const auth = useUserStore((state) => state)
  const navigate = useNavigate()

  const portalStore = usePortalStore((state) => state)
  const handleRedirection = (key) => {
    if (getLoginToken() || auth?.isAuthenticate) {
      if (key !== '') navigate(`/${key}`)
    } else {
      portalStore.openPortal(
        () => (
          <Suspense fallback={null}>
            <Signup />
          </Suspense>
        ),
        'loginModal'
      )
    }
  }
  const showCountdown = info.isCountDown && new Date(info?.endDate) > new Date(info?.startDate)

  return (
    <Grid>
      <Grid
        onClick={() => handleRedirection(info?.navigateRoute)}
        role='button'
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault()
            handleRedirection(info?.navigateRoute)
          }
        }}
        sx={{
          cursor: 'pointer',
          transition: 'transform 0.2s ease',
          '&:hover': {
            transform: 'scale(1.02)'
          },
          '&:focus': {
            outline: '2px solid #FDB72E',
            outlineOffset: '2px'
          }
        }}
      >
        <LazyImage
          src={info?.bannerImage}
          alt={info.textOne || 'Banner Image'}
          lazy={index !== 0}
          style={{
            width: '100%',
            height: 'auto',
            aspectRatio: '15/4',
            objectFit: 'cover',
            display: 'block'
          }}
        />
        <Grid className='bannerTextLobbySlider'>
          <Box>
            <Typography>
              {showCountdown && (
                <Suspense fallback={null}>
                  <CountDownTimer eventDateTime={info.endDate} />
                </Suspense>
              )}
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </Grid>
  )
}

export default DesktopBanner
