import React, { memo, useRef, useState, useEffect } from 'react'
import '../../../src/App.css'
import { Grid } from '@mui/material'
import { Swiper, SwiperSlide } from 'swiper/react'
import 'swiper/css'
import 'swiper/css/effect-coverflow'
import { EffectCoverflow, Autoplay } from 'swiper/modules'
import { defaultbanner } from '../../components/ui-kit/icons/banner'
import useStyles from './style'
import { ArrowBackIos, ArrowForwardIos } from '@mui/icons-material'
import useGetDeviceType from '../../utils/useGetDeviceType'
import MobileBanner from './MobileBanner'
import DesktopBanner from './DesktopBanner'
import { useBannerStore } from '../../store/useBannerSlice'
import LazyImage from '../../utils/lazyImage'
import BannerSkeleton from './BannerSkeleton'

/* eslint-disable multiline-ternary */

const BannerManagement = ({ bannerData = [] }) => {
  const { isInitialized } = useBannerStore()
  const swiperRef = useRef(null)
  const classes = useStyles()
  const { isMobile } = useGetDeviceType()
  const [isInitialLoad, setIsInitialLoad] = useState(true)

  // Track when banner data is loaded
  useEffect(() => {
    // Use the isInitialized flag from the store for more reliable loading detection
    if (isInitialized) {
      // Add a small delay to show skeleton briefly for better UX
      const timer = setTimeout(() => {
        setIsInitialLoad(false)
      }, 500) // Slightly longer delay for better visual feedback

      return () => clearTimeout(timer)
    }
  }, [isInitialized])

  const updateTrackingAttribute = (swiper) => {
    swiper.slides.forEach((slide) => {
      slide.removeAttribute('data-tracking')
      slide.removeAttribute('data-tracking-banner-position')
      slide.removeAttribute('data-tracking-banner-list')
    })

    if (bannerData.length === 1) {
      const singleBanner = bannerData[0]
      const activeSlide = swiper.slides[0]
      if (activeSlide) {
        activeSlide.setAttribute('data-tracking', `Store.${singleBanner?.pageBannerId}.Banner`)
        activeSlide.setAttribute('data-tracking-banner-position', 0)
        activeSlide.setAttribute('data-tracking-banner-list', 'User.Store.Main')
      }
    } else {
      const realIndex = swiper.realIndex
      const activeSlide = swiper.slides[swiper.activeIndex]
      if (activeSlide && bannerData[realIndex]) {
        const bannerInfo = bannerData[realIndex]

        activeSlide.setAttribute('data-tracking', `Store.${bannerInfo?.pageBannerId}.Banner`)
        activeSlide.setAttribute('data-tracking-banner-position', realIndex)
        activeSlide.setAttribute('data-tracking-banner-list', 'User.Store.Main')
      }
    }
  }

  const MemoizedSwiperSlide = memo(({ info, index }) => (
    <Grid className={classes.bannerSlideContainer}>
      {isMobile ? <MobileBanner info={info} index={index} /> : <DesktopBanner info={info} index={index} />}
    </Grid>
  ))

  // Show skeleton during initial load
  if (isInitialLoad) {
    return (
      <Grid className={classes.bannerLobbySlider}>
        <BannerSkeleton />
      </Grid>
    )
  }

  return (
    <Grid className={classes.bannerLobbySlider}>
      {bannerData && bannerData.length > 0 ? (
        <Grid container spacing={0.3} className='lobby-banner-wrap'>
          <Grid item xs={12}>
            <Swiper
              grabCursor
              slidesPerView={1}
              spaceBetween={30}
              centeredSlides
              loop={bannerData.length > 1}
              effect='coverflow'
              coverflowEffect={{
                rotate: 50,
                stretch: 0,
                depth: 100,
                modifier: 1,
                slideShadows: false
              }}
              autoplay={{ delay: 5000, disableOnInteraction: false }}
              modules={[Autoplay, EffectCoverflow]}
              className='mySwiper'
              onSwiper={(swiper) => {
                swiperRef.current = swiper
                updateTrackingAttribute(swiper)
              }}
            >
              {bannerData.map((info, index) => (
                <SwiperSlide
                  className='lobby-slider-section'
                  key={info.pageBannerId}
                  data-tracking={`Store.${info.pageBannerId}.Banner`}
                  data-tracking-banner-id={info.pageBannerId}
                  data-tracking-banner-list='User.Store.Main'
                  data-tracking-banner-position={index}
                >
                  <MemoizedSwiperSlide info={info} index={index} key={info.pageBannerId} />
                </SwiperSlide>
              ))}
            </Swiper>

            {bannerData.length > 1 && (
              <>
                <button className='banner-prev-btn' onClick={() => swiperRef.current?.slidePrev()}>
                  <ArrowBackIos />
                </button>
                <button className='banner-next-btn' onClick={() => swiperRef.current?.slideNext()}>
                  <ArrowForwardIos />
                </button>
              </>
            )}
          </Grid>
        </Grid>
      ) : (
        <Grid
          data-tracking='Store.0.Banner'
          data-tracking-banner-position='0'
          data-tracking-banner-list='User.Store.Main'
          className={classes.defaultBannerContainer}
        >
          <LazyImage
            src={defaultbanner}
            alt='banner-default'
            lazy={false}
            style={{
              width: '100%',
              height: 'auto',
              aspectRatio: isMobile ? '375/161' : '15/4',
              objectFit: 'cover'
            }}
          />
        </Grid>
      )}
    </Grid>
  )
}

export default BannerManagement
