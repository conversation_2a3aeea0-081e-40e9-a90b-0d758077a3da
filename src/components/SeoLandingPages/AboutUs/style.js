import { makeStyles } from '@mui/styles'

import { AboutUs } from '../../ui-kit/icons/opImages'
export default makeStyles((theme) => ({
  aboutusWrapper: {
    padding: '5rem 0rem',
    width: '100%',

    [theme.breakpoints.down('md')]: {
      padding: '5.25rem 0rem 2rem 0',
      backgroundSize: 'cover'
    },
    '& .bannerWrap': {
      position: 'relative',
      padding: '6rem 3rem',
      overflow: 'hidden',
      [theme.breakpoints.down('md')]: {
        padding: '3rem 0rem'
      },
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundImage: `url(${AboutUs})`,
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
        zIndex: -2
      },
      '&::after': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: '#000000CC',
        zIndex: -1
      },
      '& .bannerContent ': {
        height: '100%',
        width: '100%',
        maxWidth: '66.5rem',
        display: 'flex',
        flexDirection: 'column',
        padding: '0 1rem',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        margin: '0 auto',
        '& h3': {
          fontSize: '3rem',
          fontWeight: '700',
          color: theme.colors.YellowishOrange,
          [theme.breakpoints.down('md')]: {
            fontSize: '1.75rem'
          }
        },
        '& h5': {
          fontSize: '1.75rem',
          fontWeight: '700',
          marginBottom: '1.5rem',
          [theme.breakpoints.down('md')]: {
            fontSize: '1.25rem'
          }
        },
        '& p': {
          fontSize: '1.25rem',
          fontWeight: '500',
          color: theme.colors.textWhite,
          [theme.breakpoints.down('md')]: {
            fontSize: '0.875rem'
          }
        }
      }
    },
    '& .aboutus-content': {
      maxWidth: '72rem',
      margin: '0 auto',
      padding: '4.25rem 1rem 0 1rem',
      [theme.breakpoints.down('md')]: {
        padding: '0rem 1rem 0 1rem'
      },
      '& h3': {
        fontSize: '1.75rem',
        fontWeight: '700',
        color: theme.colors.YellowishOrange,
        marginBottom: '1rem',
        [theme.breakpoints.down('md')]: {
          fontSize: '1rem',
          marginTop: '1.5rem'
        }
      },
      '& h2': {
        fontSize: '1.75rem',
        fontWeight: '700',
        // marginBottom: '1rem',
        textAlign: 'center',
        [theme.breakpoints.down('md')]: {
          fontSize: '1rem',
          marginTop: '1.5rem'
        },
        '& span': {
          color: theme.colors.YellowishOrange
        },
        '&.pointer': {
          paddingLeft: '1rem',
          textAlign: 'left',
          '&:before': {
            content: '""',
            top: '12px',
            left: '0px',
            position: 'absolute',
            height: '9px',
            width: '7px',
            background: theme.colors.YellowishOrange,
            [theme.breakpoints.down('md')]: {
              top: '6px',
              height: '6px',
              width: '6px'
            }
          }
        }
      },
      '& p': {
        fontSize: '1.25rem',
        fontWeight: '500',
        lineHeight: '1.875rem',
        color: theme.colors.textWhite,
        [theme.breakpoints.down('md')]: {
          fontSize: '0.875rem',
          lineHeight: '1.5'
        },
        '&.pointer': {
          paddingLeft: '2rem',
          '&:before': {
            content: '""',
            top: '12px',
            left: '1rem ',
            position: 'absolute',
            height: '6px',
            width: '6px',
            background: theme.colors.YellowishOrange,
            borderRadius: '8px',
            [theme.breakpoints.down('md')]: {
              top: '8px',
              height: '5px',
              width: '5px'
            }
          }
        },
        '&.white-pointer': {
          '&:before': {
            background: '#fff !important'
          }
        }
      },
      '& .about-img': {
        maxHeight: '400px',
        height: '400px', // Fixed height to prevent layout shift
        borderRadius: '1.5rem',
        overflow: 'hidden',
        boxShadow: '4px 4px 4px 0px #FDB72E99',
        lineHeight: '1',
        position: 'relative',
        backgroundColor: '#1a1a1a', // Placeholder background
        [theme.breakpoints.down('md')]: {
          height: '250px',
          maxHeight: '250px'
        },
        '& img': {
          height: '100%',
          width: '100%',
          objectFit: 'cover',
          objectPosition: 'center',
          transition: 'opacity 0.3s ease-in-out'
        }
      },
      '& .info-section': {
        marginTop: '3rem',
        padding: '1.75rem 1.25rem',
        background: '#222222',
        borderRadius: '0.5rem',
        '& .info-box': {
          height: '100%',
          '& .info-heading': {
            display: 'flex',
            gap: '1.5rem',
            alignItems: 'center',
            marginBottom: '1rem',
            [theme.breakpoints.down('md')]: {
              gap: '1rem',
              marginBottom: '0.5rem'
            },
            '& img': {
              width: '3rem',
              [theme.breakpoints.down('md')]: {
                width: '2rem'
              }
            },
            '& h4': {
              fontSize: '1.5rem',
              fontWeight: '700',
              [theme.breakpoints.down('md')]: {
                fontSize: '1.25rem'
              }
            }
          },
          '& p': {
            fontSize: '1.175rem',
            fontWeight: '500',
            // lineHeight: '1.5',
            [theme.breakpoints.down('md')]: {
              fontSize: '1rem'
            }
          },
          '&.border-left': {
            position: 'relative',
            '&:after': {
              height: '100%',
              width: '1px',
              position: 'absolute',
              content: '""',
              background: '#6A6A6A',
              right: '-1.25rem',
              top: '0',
              [theme.breakpoints.down('md')]: {
                height: '1px',
                width: '100%',
                top: 'auto',
                right: 'auto',
                bottom: '-1rem'
              }
            }
          }
        }
      },
      '& .space-down': {
        marginBottom: '1rem'
      }
    },
    // '& .about-wrap': {
    //   margin: '0 auto',
    //   maxWidth: '90rem'
    // },
    // '& h1': {
    //   fontSize: '3.75rem',
    //   fontWeight: '700',
    //   fontFamily: 'Ubuntu',
    //   textAlign: 'center',
    //   color: theme.colors.YellowishOrange,
    //   [theme.breakpoints.down('md')]: {
    //     fontSize: '2rem'
    //   },
    //   marginBottom: '2.75rem'
    // },
    // '& h5': {
    //   fontSize: '1.875rem',
    //   fontWeight: '700',
    //   color: theme.colors.YellowishOrange,
    //   [theme.breakpoints.down('md')]: {
    //     fontSize: '1.25rem',
    //     marginTop: '1.5rem'
    //   },
    //   marginTop: '3rem',
    //   marginBottom: '1rem'
    // },
    // '& .head-p': {
    //   fontSize: '1.4375rem',
    //   fontWeight: '600',
    //   //   maxWidth: '34.5rem',
    //   color: theme.colors.textWhite,
    //   margin: '0 auto',
    //   marginBottom: '1rem',
    //   [theme.breakpoints.down('md')]: {
    //     fontSize: '1rem',
    //     marginBottom: '1rem'
    //   }
    // }
    '& .yellow-text': {
      color: '#FDB72E'
    },
    '& .heading-p': {
      textAlign: 'center',
      marginBottom: '2.5rem',
      [theme.breakpoints.down('md')]: {
        marginBottom: '0'
      }
    },
    '& .space-down': {
      marginBottom: '1.5rem'
    },
    '& a': {
      color: '#23A8FC',
      cursor: 'pointer'
    }
  },
  headingH3: {
    textAlign: 'left',
    fontSize: '1.75rem',
    fontWeight: '700'
  }
}))
